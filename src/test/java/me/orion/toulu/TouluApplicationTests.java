package me.orion.toulu;

import com.alibaba.fastjson2.JSONObject;
import me.orion.toulu.service.HarParseService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.*;
import java.util.Map;

@SpringBootTest
class TouluApplicationTests {

    @Test
    void contextLoads() {
    }

    public static void main(String[] args) throws IOException {
        File file = new File("C:\\Users\\<USER>\\Desktop\\大潮.har");
        // 获取file的流
        try (InputStream inputStream = new FileInputStream(file)) {
            HarParseService harParseService = new HarParseService();
            Map<String, Object> stringObjectMap = harParseService.parseHarFile(inputStream);
            System.out.println(JSONObject.toJSONString(stringObjectMap));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
