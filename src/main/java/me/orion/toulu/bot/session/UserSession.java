package me.orion.toulu.bot.session;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户会话实体
 * 存储用户的会话状态和上下文信息
 * 
 * <AUTHOR> 4.0 sonnet
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserSession {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 聊天ID
     */
    private Long chatId;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 会话状态
     */
    private SessionState state;
    
    /**
     * 当前正在执行的命令
     */
    private String currentCommand;
    
    /**
     * 会话数据存储
     */
    @Builder.Default
    private Map<String, Object> data = new HashMap<>();
    
    /**
     * 会话创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 最后活跃时间
     */
    private LocalDateTime lastActiveAt;
    
    /**
     * 会话过期时间（分钟）
     */
    @Builder.Default
    private int timeoutMinutes = 30;
    
    /**
     * 用户语言偏好
     */
    @Builder.Default
    private String language = "zh";
    
    /**
     * 用户权限级别
     */
    @Builder.Default
    private UserRole role = UserRole.USER;
    
    /**
     * 会话统计信息
     */
    @Builder.Default
    private SessionStats stats = new SessionStats();
    
    /**
     * 会话状态枚举
     */
    public enum SessionState {
        /**
         * 空闲状态
         */
        IDLE,
        
        /**
         * 等待用户输入
         */
        WAITING_INPUT,
        
        /**
         * 处理命令中
         */
        PROCESSING_COMMAND,
        
        /**
         * 等待确认
         */
        WAITING_CONFIRMATION,
        
        /**
         * 会话已过期
         */
        EXPIRED
    }
    
    /**
     * 用户角色枚举
     */
    public enum UserRole {
        /**
         * 普通用户
         */
        USER,
        
        /**
         * 管理员
         */
        ADMIN,
        
        /**
         * 超级管理员
         */
        SUPER_ADMIN
    }
    
    /**
     * 会话统计信息
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SessionStats {
        /**
         * 命令执行次数
         */
        @Builder.Default
        private int commandCount = 0;
        
        /**
         * 消息发送次数
         */
        @Builder.Default
        private int messageCount = 0;
        
        /**
         * 错误次数
         */
        @Builder.Default
        private int errorCount = 0;
        
        /**
         * 会话持续时间（分钟）
         */
        @Builder.Default
        private long durationMinutes = 0;
    }
    
    /**
     * 检查会话是否过期
     */
    public boolean isExpired() {
        if (lastActiveAt == null) {
            return true;
        }
        return lastActiveAt.plusMinutes(timeoutMinutes).isBefore(LocalDateTime.now());
    }
    
    /**
     * 更新最后活跃时间
     */
    public void updateLastActive() {
        this.lastActiveAt = LocalDateTime.now();
    }
    
    /**
     * 设置会话数据
     */
    public void setData(String key, Object value) {
        this.data.put(key, value);
    }
    
    /**
     * 获取会话数据
     */
    @SuppressWarnings("unchecked")
    public <T> T getData(String key, Class<T> type) {
        Object value = this.data.get(key);
        if (value != null && type.isInstance(value)) {
            return (T) value;
        }
        return null;
    }
    
    /**
     * 获取会话数据（带默认值）
     */
    public <T> T getData(String key, Class<T> type, T defaultValue) {
        T value = getData(key, type);
        return value != null ? value : defaultValue;
    }
    
    /**
     * 移除会话数据
     */
    public void removeData(String key) {
        this.data.remove(key);
    }
    
    /**
     * 清空会话数据
     */
    public void clearData() {
        this.data.clear();
    }
    
    /**
     * 增加命令计数
     */
    public void incrementCommandCount() {
        this.stats.commandCount++;
    }
    
    /**
     * 增加消息计数
     */
    public void incrementMessageCount() {
        this.stats.messageCount++;
    }
    
    /**
     * 增加错误计数
     */
    public void incrementErrorCount() {
        this.stats.errorCount++;
    }
    
    /**
     * 计算会话持续时间
     */
    public void updateDuration() {
        if (createdAt != null) {
            this.stats.durationMinutes = java.time.Duration.between(createdAt, LocalDateTime.now()).toMinutes();
        }
    }
}
