package me.orion.toulu.bot.session;

import lombok.extern.slf4j.Slf4j;
import me.orion.toulu.bot.BotConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 用户会话管理服务
 * 负责用户会话的创建、管理和清理
 * 
 * <AUTHOR> 4.0 sonnet
 */
@Service
@Slf4j
public class SessionManager {
    
    @Autowired
    private BotConfig botConfig;
    
    /**
     * 会话存储 - 使用 ConcurrentHashMap 保证线程安全
     */
    private final Map<Long, UserSession> sessions = new ConcurrentHashMap<>();
    
    /**
     * 会话计数器
     */
    private final AtomicLong sessionCounter = new AtomicLong(0);
    
    /**
     * 获取或创建用户会话
     */
    public UserSession getOrCreateSession(Long userId, Long chatId, String username) {
        UserSession session = sessions.get(userId);
        
        if (session == null || session.isExpired()) {
            session = createNewSession(userId, chatId, username);
            sessions.put(userId, session);
            log.debug("为用户 {} 创建新会话", userId);
        } else {
            // 更新最后活跃时间
            session.updateLastActive();
            log.debug("更新用户 {} 的会话活跃时间", userId);
        }
        
        return session;
    }
    
    /**
     * 获取现有会话
     */
    public UserSession getSession(Long userId) {
        UserSession session = sessions.get(userId);
        if (session != null && !session.isExpired()) {
            session.updateLastActive();
            return session;
        }
        return null;
    }
    
    /**
     * 创建新会话
     */
    private UserSession createNewSession(Long userId, Long chatId, String username) {
        LocalDateTime now = LocalDateTime.now();
        
        UserSession session = UserSession.builder()
                .userId(userId)
                .chatId(chatId)
                .username(username)
                .state(UserSession.SessionState.IDLE)
                .createdAt(now)
                .lastActiveAt(now)
                .role(determineUserRole(userId))
                .build();
        
        sessionCounter.incrementAndGet();
        return session;
    }
    
    /**
     * 确定用户角色
     */
    private UserSession.UserRole determineUserRole(Long userId) {
        if (botConfig.isAdmin(userId)) {
            return UserSession.UserRole.ADMIN;
        }
        return UserSession.UserRole.USER;
    }
    
    /**
     * 更新会话状态
     */
    public void updateSessionState(Long userId, UserSession.SessionState state) {
        UserSession session = getSession(userId);
        if (session != null) {
            session.setState(state);
            session.updateLastActive();
            log.debug("用户 {} 会话状态更新为: {}", userId, state);
        }
    }
    
    /**
     * 设置当前命令
     */
    public void setCurrentCommand(Long userId, String command) {
        UserSession session = getSession(userId);
        if (session != null) {
            session.setCurrentCommand(command);
            session.updateLastActive();
        }
    }
    
    /**
     * 增加命令计数
     */
    public void incrementCommandCount(Long userId) {
        UserSession session = getSession(userId);
        if (session != null) {
            session.incrementCommandCount();
            session.updateLastActive();
        }
    }
    
    /**
     * 增加消息计数
     */
    public void incrementMessageCount(Long userId) {
        UserSession session = getSession(userId);
        if (session != null) {
            session.incrementMessageCount();
            session.updateLastActive();
        }
    }
    
    /**
     * 增加错误计数
     */
    public void incrementErrorCount(Long userId) {
        UserSession session = getSession(userId);
        if (session != null) {
            session.incrementErrorCount();
            session.updateLastActive();
        }
    }
    
    /**
     * 设置会话数据
     */
    public void setSessionData(Long userId, String key, Object value) {
        UserSession session = getSession(userId);
        if (session != null) {
            session.setData(key, value);
            session.updateLastActive();
        }
    }
    
    /**
     * 获取会话数据
     */
    public <T> T getSessionData(Long userId, String key, Class<T> type) {
        UserSession session = getSession(userId);
        return session != null ? session.getData(key, type) : null;
    }
    
    /**
     * 移除会话
     */
    public void removeSession(Long userId) {
        UserSession session = sessions.remove(userId);
        if (session != null) {
            log.info("移除用户 {} 的会话", userId);
        }
    }
    
    /**
     * 清理过期会话 - 每5分钟执行一次
     */
    @Scheduled(fixedRate = 300000) // 5分钟
    public void cleanupExpiredSessions() {
        int removedCount = 0;
        
        for (Map.Entry<Long, UserSession> entry : sessions.entrySet()) {
            UserSession session = entry.getValue();
            if (session.isExpired()) {
                sessions.remove(entry.getKey());
                removedCount++;
                log.debug("清理过期会话: 用户 {}", entry.getKey());
            }
        }
        
        if (removedCount > 0) {
            log.info("清理了 {} 个过期会话，当前活跃会话数: {}", removedCount, sessions.size());
        }
    }
    
    /**
     * 获取会话统计信息
     */
    public SessionStatistics getStatistics() {
        int activeSessionCount = sessions.size();
        long totalSessionsCreated = sessionCounter.get();
        
        int adminSessionCount = (int) sessions.values().stream()
                .filter(session -> session.getRole() == UserSession.UserRole.ADMIN)
                .count();
        
        return SessionStatistics.builder()
                .activeSessionCount(activeSessionCount)
                .totalSessionsCreated(totalSessionsCreated)
                .adminSessionCount(adminSessionCount)
                .userSessionCount(activeSessionCount - adminSessionCount)
                .build();
    }
    
    /**
     * 会话统计信息
     */
    @lombok.Data
    @lombok.Builder
    public static class SessionStatistics {
        private int activeSessionCount;
        private long totalSessionsCreated;
        private int adminSessionCount;
        private int userSessionCount;
    }
}
