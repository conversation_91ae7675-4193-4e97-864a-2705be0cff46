package me.orion.toulu.bot.template;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 消息模板系统
 * 支持多语言和动态参数替换
 * 
 * <AUTHOR> 4.0 sonnet
 */
@Component
@Slf4j
public class MessageTemplate {
    
    /**
     * 模板存储 - 语言 -> 模板键 -> 模板内容
     */
    private final Map<String, Map<String, String>> templates = new HashMap<>();
    
    /**
     * 参数替换模式
     */
    private static final Pattern PARAM_PATTERN = Pattern.compile("\\{([^}]+)\\}");
    
    /**
     * 默认语言
     */
    private static final String DEFAULT_LANGUAGE = "zh";
    
    /**
     * 初始化模板
     */
    @PostConstruct
    public void initializeTemplates() {
        log.info("初始化消息模板...");
        
        // 中文模板
        Map<String, String> zhTemplates = new HashMap<>();
        zhTemplates.put("welcome", "👋 你好，<b>{username}</b>！\n\n🤖 欢迎使用 Toulu Bot！\n\n发送 /help 查看可用命令。");
        zhTemplates.put("help.header", "📋 <b>可用命令</b>\n\n");
        zhTemplates.put("help.user.header", "👤 <b>用户命令：</b>\n");
        zhTemplates.put("help.admin.header", "\n👑 <b>管理员命令：</b>\n");
        zhTemplates.put("help.footer", "\n💡 <i>提示：点击命令可以直接使用</i>");
        zhTemplates.put("help.command.format", "🔹 {command} - {description}\n");
        zhTemplates.put("help.admin.command.format", "🔸 {command} - {description}\n");
        
        zhTemplates.put("error.unknown_command", "❓ <b>未知命令</b>\n\n抱歉，我不认识命令 <code>{command}</code>\n\n💡 发送 /help 查看所有可用命令");
        zhTemplates.put("error.no_permission", "❌ 您没有权限执行此命令");
        zhTemplates.put("error.command_failed", "❌ 命令执行失败，请稍后重试");
        zhTemplates.put("error.session_expired", "⏰ 您的会话已过期，请重新开始");
        
        zhTemplates.put("success.command_executed", "✅ 命令执行成功");
        zhTemplates.put("info.processing", "⏳ 正在处理您的请求...");
        zhTemplates.put("info.session_created", "🆕 已为您创建新的会话");
        
        zhTemplates.put("stats.header", "📊 <b>使用统计</b>\n\n");
        zhTemplates.put("stats.user.format", "👤 <b>个人统计：</b>\n• 命令执行次数: {commandCount}\n• 消息发送次数: {messageCount}\n• 会话持续时间: {duration} 分钟\n\n");
        zhTemplates.put("stats.system.format", "🤖 <b>系统统计：</b>\n• 活跃会话数: {activeSessions}\n• 总会话数: {totalSessions}\n• 管理员会话: {adminSessions}\n");
        
        zhTemplates.put("about.header", "ℹ️ <b>关于 Toulu Bot</b>\n\n");
        zhTemplates.put("about.info", "🤖 <b>机器人信息：</b>\n• 名称: Toulu Bot\n• 版本: 1.0.0\n• 开发者: Claude 4.0 sonnet\n• 框架: Spring Boot + Telegram Bot API\n\n");
        zhTemplates.put("about.features", "🚀 <b>主要功能：</b>\n• 智能命令处理\n• 多用户权限管理\n• 可扩展的插件架构\n• 完善的错误处理\n• 用户会话管理\n• 多语言支持\n\n");
        zhTemplates.put("about.tech", "💡 <b>技术特性：</b>\n• 基于策略模式的命令处理\n• 支持异步消息处理\n• 内置重试机制\n• 完整的日志记录\n• 会话状态管理\n\n");
        
        templates.put("zh", zhTemplates);
        
        // 英文模板
        Map<String, String> enTemplates = new HashMap<>();
        enTemplates.put("welcome", "👋 Hello, <b>{username}</b>!\n\n🤖 Welcome to Toulu Bot!\n\nSend /help to see available commands.");
        enTemplates.put("help.header", "📋 <b>Available Commands</b>\n\n");
        enTemplates.put("help.user.header", "👤 <b>User Commands:</b>\n");
        enTemplates.put("help.admin.header", "\n👑 <b>Admin Commands:</b>\n");
        enTemplates.put("help.footer", "\n💡 <i>Tip: Click on commands to use them directly</i>");
        enTemplates.put("help.command.format", "🔹 {command} - {description}\n");
        enTemplates.put("help.admin.command.format", "🔸 {command} - {description}\n");
        
        enTemplates.put("error.unknown_command", "❓ <b>Unknown Command</b>\n\nSorry, I don't recognize the command <code>{command}</code>\n\n💡 Send /help to see all available commands");
        enTemplates.put("error.no_permission", "❌ You don't have permission to execute this command");
        enTemplates.put("error.command_failed", "❌ Command execution failed, please try again later");
        enTemplates.put("error.session_expired", "⏰ Your session has expired, please start again");
        
        enTemplates.put("success.command_executed", "✅ Command executed successfully");
        enTemplates.put("info.processing", "⏳ Processing your request...");
        enTemplates.put("info.session_created", "🆕 New session created for you");
        
        templates.put("en", enTemplates);
        
        log.info("消息模板初始化完成，支持语言: {}", templates.keySet());
    }
    
    /**
     * 获取模板消息
     */
    public String getMessage(String key, String language) {
        return getMessage(key, language, new HashMap<>());
    }
    
    /**
     * 获取模板消息（带参数）
     */
    public String getMessage(String key, String language, Map<String, Object> params) {
        // 获取模板
        String template = getTemplate(key, language);
        
        // 替换参数
        return replaceParameters(template, params);
    }
    
    /**
     * 获取模板消息（使用默认语言）
     */
    public String getMessage(String key) {
        return getMessage(key, DEFAULT_LANGUAGE);
    }
    
    /**
     * 获取模板消息（使用默认语言，带参数）
     */
    public String getMessage(String key, Map<String, Object> params) {
        return getMessage(key, DEFAULT_LANGUAGE, params);
    }
    
    /**
     * 获取原始模板
     */
    private String getTemplate(String key, String language) {
        Map<String, String> languageTemplates = templates.get(language);
        
        if (languageTemplates == null) {
            log.warn("不支持的语言: {}，使用默认语言: {}", language, DEFAULT_LANGUAGE);
            languageTemplates = templates.get(DEFAULT_LANGUAGE);
        }
        
        String template = languageTemplates.get(key);
        if (template == null) {
            log.warn("未找到模板: {} (语言: {})，返回默认消息", key, language);
            return "消息模板未找到: " + key;
        }
        
        return template;
    }
    
    /**
     * 替换模板参数
     */
    private String replaceParameters(String template, Map<String, Object> params) {
        if (params == null || params.isEmpty()) {
            return template;
        }
        
        Matcher matcher = PARAM_PATTERN.matcher(template);
        StringBuffer result = new StringBuffer();
        
        while (matcher.find()) {
            String paramName = matcher.group(1);
            Object paramValue = params.get(paramName);
            
            String replacement = paramValue != null ? paramValue.toString() : "{" + paramName + "}";
            matcher.appendReplacement(result, Matcher.quoteReplacement(replacement));
        }
        
        matcher.appendTail(result);
        return result.toString();
    }
    
    /**
     * 检查是否支持指定语言
     */
    public boolean isLanguageSupported(String language) {
        return templates.containsKey(language);
    }
    
    /**
     * 获取支持的语言列表
     */
    public String[] getSupportedLanguages() {
        return templates.keySet().toArray(new String[0]);
    }
    
    /**
     * 添加自定义模板
     */
    public void addTemplate(String language, String key, String template) {
        templates.computeIfAbsent(language, k -> new HashMap<>()).put(key, template);
        log.debug("添加自定义模板: {} -> {} (语言: {})", key, template, language);
    }
}
