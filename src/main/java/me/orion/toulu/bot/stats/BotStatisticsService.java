package me.orion.toulu.bot.stats;

import lombok.Data;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import me.orion.toulu.bot.command.BotCommand;
import me.orion.toulu.bot.session.SessionManager;
import me.orion.toulu.bot.session.UserSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Bot 统计服务
 * 收集和管理机器人使用统计信息
 * 
 * <AUTHOR> 4.0 sonnet
 */
@Service
@Slf4j
public class BotStatisticsService {
    
    @Autowired
    private SessionManager sessionManager;
    
    /**
     * 命令使用统计
     */
    private final Map<BotCommand, AtomicLong> commandStats = new ConcurrentHashMap<>();
    
    /**
     * 用户命令使用统计
     */
    private final Map<Long, Map<BotCommand, AtomicLong>> userCommandStats = new ConcurrentHashMap<>();
    
    /**
     * 总消息数
     */
    private final AtomicLong totalMessages = new AtomicLong(0);
    
    /**
     * 总错误数
     */
    private final AtomicLong totalErrors = new AtomicLong(0);
    
    /**
     * 服务启动时间
     */
    private final LocalDateTime startTime = LocalDateTime.now();
    
    /**
     * 记录命令使用
     */
    public void recordCommandUsage(Long userId, BotCommand command) {
        // 全局统计
        commandStats.computeIfAbsent(command, k -> new AtomicLong(0)).incrementAndGet();
        
        // 用户统计
        userCommandStats.computeIfAbsent(userId, k -> new ConcurrentHashMap<>())
                .computeIfAbsent(command, k -> new AtomicLong(0))
                .incrementAndGet();
        
        // 更新会话统计
        sessionManager.incrementCommandCount(userId);
        
        log.debug("记录命令使用: 用户 {} 执行命令 {}", userId, command.getCommand());
    }
    
    /**
     * 记录消息发送
     */
    public void recordMessageSent(Long userId) {
        totalMessages.incrementAndGet();
        sessionManager.incrementMessageCount(userId);
        
        log.debug("记录消息发送: 用户 {}", userId);
    }
    
    /**
     * 记录错误
     */
    public void recordError(Long userId) {
        totalErrors.incrementAndGet();
        if (userId != null) {
            sessionManager.incrementErrorCount(userId);
        }
        
        log.debug("记录错误: 用户 {}", userId);
    }
    
    /**
     * 获取全局统计信息
     */
    public GlobalStatistics getGlobalStatistics() {
        SessionManager.SessionStatistics sessionStats = sessionManager.getStatistics();
        
        // 计算最受欢迎的命令
        BotCommand mostUsedCommand = commandStats.entrySet().stream()
                .max(Map.Entry.comparingByValue((a, b) -> Long.compare(a.get(), b.get())))
                .map(Map.Entry::getKey)
                .orElse(null);
        
        long totalCommandUsage = commandStats.values().stream()
                .mapToLong(AtomicLong::get)
                .sum();
        
        return GlobalStatistics.builder()
                .totalMessages(totalMessages.get())
                .totalErrors(totalErrors.get())
                .totalCommandUsage(totalCommandUsage)
                .activeSessions(sessionStats.getActiveSessionCount())
                .totalSessionsCreated(sessionStats.getTotalSessionsCreated())
                .adminSessions(sessionStats.getAdminSessionCount())
                .userSessions(sessionStats.getUserSessionCount())
                .mostUsedCommand(mostUsedCommand)
                .uptimeMinutes(java.time.Duration.between(startTime, LocalDateTime.now()).toMinutes())
                .build();
    }
    
    /**
     * 获取用户统计信息
     */
    public UserStatistics getUserStatistics(Long userId) {
        UserSession session = sessionManager.getSession(userId);
        if (session == null) {
            return null;
        }
        
        Map<BotCommand, AtomicLong> userCommands = userCommandStats.get(userId);
        long totalUserCommands = 0;
        BotCommand favoriteCommand = null;
        
        if (userCommands != null) {
            totalUserCommands = userCommands.values().stream()
                    .mapToLong(AtomicLong::get)
                    .sum();
            
            favoriteCommand = userCommands.entrySet().stream()
                    .max(Map.Entry.comparingByValue((a, b) -> Long.compare(a.get(), b.get())))
                    .map(Map.Entry::getKey)
                    .orElse(null);
        }
        
        session.updateDuration();
        UserSession.SessionStats sessionStats = session.getStats();
        
        return UserStatistics.builder()
                .userId(userId)
                .username(session.getUsername())
                .role(session.getRole())
                .sessionState(session.getState())
                .commandCount(sessionStats.getCommandCount())
                .messageCount(sessionStats.getMessageCount())
                .errorCount(sessionStats.getErrorCount())
                .sessionDurationMinutes(sessionStats.getDurationMinutes())
                .totalCommandUsage(totalUserCommands)
                .favoriteCommand(favoriteCommand)
                .sessionCreatedAt(session.getCreatedAt())
                .lastActiveAt(session.getLastActiveAt())
                .build();
    }
    
    /**
     * 获取命令使用统计
     */
    public Map<BotCommand, Long> getCommandStatistics() {
        Map<BotCommand, Long> result = new ConcurrentHashMap<>();
        commandStats.forEach((command, count) -> result.put(command, count.get()));
        return result;
    }
    
    /**
     * 获取用户命令使用统计
     */
    public Map<BotCommand, Long> getUserCommandStatistics(Long userId) {
        Map<BotCommand, AtomicLong> userCommands = userCommandStats.get(userId);
        if (userCommands == null) {
            return new ConcurrentHashMap<>();
        }
        
        Map<BotCommand, Long> result = new ConcurrentHashMap<>();
        userCommands.forEach((command, count) -> result.put(command, count.get()));
        return result;
    }
    
    /**
     * 重置统计信息
     */
    public void resetStatistics() {
        commandStats.clear();
        userCommandStats.clear();
        totalMessages.set(0);
        totalErrors.set(0);
        
        log.info("统计信息已重置");
    }
    
    /**
     * 全局统计信息
     */
    @Data
    @Builder
    public static class GlobalStatistics {
        private long totalMessages;
        private long totalErrors;
        private long totalCommandUsage;
        private int activeSessions;
        private long totalSessionsCreated;
        private int adminSessions;
        private int userSessions;
        private BotCommand mostUsedCommand;
        private long uptimeMinutes;
    }
    
    /**
     * 用户统计信息
     */
    @Data
    @Builder
    public static class UserStatistics {
        private Long userId;
        private String username;
        private UserSession.UserRole role;
        private UserSession.SessionState sessionState;
        private int commandCount;
        private int messageCount;
        private int errorCount;
        private long sessionDurationMinutes;
        private long totalCommandUsage;
        private BotCommand favoriteCommand;
        private LocalDateTime sessionCreatedAt;
        private LocalDateTime lastActiveAt;
    }
}
