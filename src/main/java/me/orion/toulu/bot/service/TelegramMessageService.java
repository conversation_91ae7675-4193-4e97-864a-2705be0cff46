package me.orion.toulu.bot.service;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Timer;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.telegram.telegrambots.meta.api.methods.ParseMode;
import org.telegram.telegrambots.meta.api.methods.send.SendMessage;
import org.telegram.telegrambots.meta.api.methods.send.SendPhoto;
import org.telegram.telegrambots.meta.api.objects.InputFile;
import org.telegram.telegrambots.meta.api.objects.replykeyboard.InlineKeyboardMarkup;
import org.telegram.telegrambots.meta.api.objects.replykeyboard.ReplyKeyboardMarkup;
import org.telegram.telegrambots.meta.exceptions.TelegramApiException;
import org.telegram.telegrambots.bots.TelegramLongPollingBot;

/**
 * Telegram 消息发送服务
 * 提供统一的消息发送接口，包含重试机制和错误处理
 * 
 * <AUTHOR> 4.0 sonnet
 */
@Setter
@Service
@Slf4j
@RequiredArgsConstructor
public class TelegramMessageService {

    /**
     * -- SETTER --
     *  设置 bot 实例（由于循环依赖，使用 setter 注入）
     */
    private TelegramLongPollingBot bot;

    @Autowired
    private Counter messageSentCounter;

    @Autowired
    private Timer messageProcessingTimer;

    /**
     * 发送简单文本消息
     */
    @Retryable(value = {TelegramApiException.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000))
    public boolean sendMessage(Long chatId, String text) {
        return sendMessage(chatId, text, null);
    }

    /**
     * 异步发送消息
     */
    @Async("botMessageExecutor")
    public void sendMessageAsync(Long chatId, String text) {
        Timer.Sample sample = Timer.start();
        try {
            sendMessage(chatId, text);
        } finally {
            sample.stop(messageProcessingTimer);
        }
    }
    
    /**
     * 发送带键盘的文本消息
     */
    @Retryable(value = {TelegramApiException.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000))
    public boolean sendMessage(Long chatId, String text, InlineKeyboardMarkup keyboard) {
        try {
            if (bot == null) {
                log.error("Bot 实例未初始化");
                return false;
            }
            
            if (chatId == null || text == null || text.trim().isEmpty()) {
                log.warn("发送消息参数无效 - ChatId: {}, Text: {}", chatId, text);
                return false;
            }
            
            SendMessage message = new SendMessage();
            message.setChatId(chatId.toString());
            message.setText(text);
            message.setParseMode(ParseMode.MARKDOWNV2);
            message.setParseMode("HTML"); // 支持 HTML 格式
            
            if (keyboard != null) {
                message.setReplyMarkup(keyboard);
            }
            
            bot.execute(message);
            messageSentCounter.increment();
            log.debug("消息发送成功 - ChatId: {}", chatId);
            return true;
            
        } catch (TelegramApiException e) {
            log.error("发送消息失败 - ChatId: {}, Text: {}, Error: {}", chatId, text, e.getMessage());
            return false;
        }
    }
    
    /**
     * 发送图片消息
     */
    @Retryable(value = {TelegramApiException.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000))
    public boolean sendPhoto(Long chatId, InputFile photo, String caption) {
        try {
            if (bot == null) {
                log.error("Bot 实例未初始化");
                return false;
            }
            
            if (chatId == null || photo == null) {
                log.warn("发送图片参数无效 - ChatId: {}, Photo: {}", chatId, photo);
                return false;
            }
            
            SendPhoto sendPhoto = new SendPhoto();
            sendPhoto.setChatId(chatId.toString());
            sendPhoto.setPhoto(photo);
            
            if (caption != null && !caption.trim().isEmpty()) {
                sendPhoto.setCaption(caption);
                sendPhoto.setParseMode("HTML");
            }
            
            bot.execute(sendPhoto);
            log.debug("图片发送成功 - ChatId: {}", chatId);
            return true;
            
        } catch (TelegramApiException e) {
            log.error("发送图片失败 - ChatId: {}, Error: {}", chatId, e.getMessage());
            return false;
        }
    }
    
    /**
     * 发送格式化消息
     */
    public boolean sendFormattedMessage(Long chatId, String template, Object... args) {
        try {
            String formattedText = String.format(template, args);
            return sendMessage(chatId, formattedText);
        } catch (Exception e) {
            log.error("格式化消息失败 - Template: {}, Args: {}", template, args, e);
            return false;
        }
    }
    
    /**
     * 发送错误消息
     */
    public boolean sendErrorMessage(Long chatId, String errorMessage) {
        String formattedError = "❌ <b>错误</b>\n\n" + errorMessage;
        return sendMessage(chatId, formattedError);
    }
    
    /**
     * 发送成功消息
     */
    public boolean sendSuccessMessage(Long chatId, String successMessage) {
        String formattedSuccess = "✅ <b>成功</b>\n\n" + successMessage;
        return sendMessage(chatId, formattedSuccess);
    }
    
    /**
     * 发送警告消息
     */
    public boolean sendWarningMessage(Long chatId, String warningMessage) {
        String formattedWarning = "⚠️ <b>警告</b>\n\n" + warningMessage;
        return sendMessage(chatId, formattedWarning);
    }
    
    /**
     * 发送信息消息
     */
    public boolean sendInfoMessage(Long chatId, String infoMessage) {
        String formattedInfo = "ℹ️ <b>信息</b>\n\n" + infoMessage;
        return sendMessage(chatId, formattedInfo);
    }
}
