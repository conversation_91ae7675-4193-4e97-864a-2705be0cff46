package me.orion.toulu.bot.exception;

import lombok.Getter;

/**
 * Bot 相关的自定义异常类
 * 
 * <AUTHOR> 4.0 sonnet
 */
@Getter
public class BotException extends RuntimeException {
    
    private final String errorCode;
    
    public BotException(String message) {
        super(message);
        this.errorCode = "GENERAL_ERROR";
    }
    
    public BotException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = "GENERAL_ERROR";
    }
    
    public BotException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
    
    public BotException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }

    /**
     * Bot 初始化异常
     */
    public static class BotInitializationException extends BotException {
        public BotInitializationException(String message) {
            super("BOT_INIT_ERROR", message);
        }
        
        public BotInitializationException(String message, Throwable cause) {
            super("BOT_INIT_ERROR", message, cause);
        }
    }
    
    /**
     * 消息发送异常
     */
    public static class MessageSendException extends BotException {
        public MessageSendException(String message) {
            super("MESSAGE_SEND_ERROR", message);
        }
        
        public MessageSendException(String message, Throwable cause) {
            super("MESSAGE_SEND_ERROR", message, cause);
        }
    }
    
    /**
     * 命令处理异常
     */
    public static class CommandProcessingException extends BotException {
        public CommandProcessingException(String message) {
            super("COMMAND_PROCESSING_ERROR", message);
        }
        
        public CommandProcessingException(String message, Throwable cause) {
            super("COMMAND_PROCESSING_ERROR", message, cause);
        }
    }
    
    /**
     * 配置异常
     */
    public static class ConfigurationException extends BotException {
        public ConfigurationException(String message) {
            super("CONFIGURATION_ERROR", message);
        }
        
        public ConfigurationException(String message, Throwable cause) {
            super("CONFIGURATION_ERROR", message, cause);
        }
    }
}
