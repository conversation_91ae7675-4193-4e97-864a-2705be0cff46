package me.orion.toulu.bot;

import lombok.Data;
import me.orion.toulu.bot.exception.BotException;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;

import jakarta.annotation.PostConstruct;

/**
 * Telegram Bot 配置类
 *
 * <AUTHOR> 4.0 sonnet
 */
@Configuration
@ConfigurationProperties(prefix = "telegram")
@Data
public class BotConfig {

    /**
     * Bot Token（必需）
     */
    private String token;

    /**
     * Bot 用户名（必需）
     */
    private String username;

    /**
     * 是否启用 Bot（默认启用）
     */
    private boolean enabled = true;

    /**
     * 连接超时时间（秒，默认 30 秒）
     */
    private int connectionTimeout = 30;

    /**
     * 读取超时时间（秒，默认 60 秒）
     */
    private int readTimeout = 60;

    /**
     * 最大重试次数（默认 3 次）
     */
    private int maxRetryAttempts = 3;

    /**
     * 重试间隔（毫秒，默认 1000ms）
     */
    private long retryDelay = 1000L;

    /**
     * 是否启用调试模式（默认关闭）
     */
    private boolean debugMode = false;

    /**
     * 允许的管理员用户 ID 列表（可选）
     */
    private String adminUserIds;

    /**
     * 欢迎消息模板
     */
    private String welcomeMessage = "🤖 欢迎使用 Toulu Bot！\n\n发送 /help 查看可用命令。";

    /**
     * 帮助消息模板
     */
    private String helpMessage = "📋 可用命令：\n\n/start - 开始使用\n/help - 显示帮助信息";

    /**
     * 配置验证
     */
    @PostConstruct
    public void validate() {
        if (!enabled) {
            return; // 如果未启用，跳过验证
        }

        if (!StringUtils.hasText(token)) {
            throw new BotException.ConfigurationException("Telegram bot token 不能为空");
        }

        if (!StringUtils.hasText(username)) {
            throw new BotException.ConfigurationException("Telegram bot username 不能为空");
        }

        if (connectionTimeout <= 0) {
            throw new BotException.ConfigurationException("连接超时时间必须大于 0");
        }

        if (readTimeout <= 0) {
            throw new BotException.ConfigurationException("读取超时时间必须大于 0");
        }

        if (maxRetryAttempts < 0) {
            throw new BotException.ConfigurationException("最大重试次数不能小于 0");
        }

        if (retryDelay < 0) {
            throw new BotException.ConfigurationException("重试间隔不能小于 0");
        }
    }

    /**
     * 检查用户是否为管理员
     */
    public boolean isAdmin(Long userId) {
        if (!StringUtils.hasText(adminUserIds) || userId == null) {
            return false;
        }

        String[] adminIds = adminUserIds.split(",");
        for (String adminId : adminIds) {
            if (adminId.trim().equals(userId.toString())) {
                return true;
            }
        }
        return false;
    }
}