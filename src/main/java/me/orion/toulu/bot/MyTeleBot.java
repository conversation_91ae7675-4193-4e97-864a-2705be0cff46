package me.orion.toulu.bot;

import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.orion.toulu.bot.command.CommandHandler;
import me.orion.toulu.bot.command.CommandHandlerFactory;
import me.orion.toulu.bot.exception.BotException;
import me.orion.toulu.bot.service.TelegramMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.telegram.telegrambots.bots.TelegramLongPollingBot;
import org.telegram.telegrambots.meta.TelegramBotsApi;
import org.telegram.telegrambots.meta.api.objects.Chat;
import org.telegram.telegrambots.meta.api.objects.Message;
import org.telegram.telegrambots.meta.api.objects.Update;
import org.telegram.telegrambots.meta.exceptions.TelegramApiException;
import org.telegram.telegrambots.updatesreceivers.DefaultBotSession;

import java.util.List;

/**
 * Telegram Bot 主类
 * 负责接收和分发消息，具备完整的错误处理和安全检查
 *
 * <AUTHOR> 4.0 sonnet
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class MyTeleBot extends TelegramLongPollingBot {

    private final BotConfig botConfig;

    @Autowired
    private TelegramMessageService messageService;

    @Autowired
    private CommandHandlerFactory commandHandlerFactory;

    @PostConstruct
    public void init() {
        try {
            // 检查配置是否启用
            if (!botConfig.isEnabled()) {
                log.info("Telegram Bot 已禁用，跳过初始化");
                return;
            }

            // 初始化消息服务
            messageService.setBot(this);

            // 注册 Bot
            TelegramBotsApi telegramBotsApi = new TelegramBotsApi(DefaultBotSession.class);
            telegramBotsApi.registerBot(this);

            log.info("🤖 Telegram Bot [{}] 启动成功", botConfig.getUsername());

        } catch (TelegramApiException e) {
            String errorMsg = String.format("Telegram Bot 启动失败: %s", e.getMessage());
            log.error(errorMsg, e);
            throw new BotException.BotInitializationException(errorMsg, e);
        } catch (Exception e) {
            String errorMsg = String.format("Bot 初始化过程中发生未知错误: %s", e.getMessage());
            log.error(errorMsg, e);
            throw new BotException.BotInitializationException(errorMsg, e);
        }
    }

    @Override
    public void onUpdateReceived(Update update) {
        try {
            // 安全性检查：确保 update 和 message 不为空
            if (update == null) {
                log.warn("收到空的 update 对象");
                return;
            }

            if (!update.hasMessage()) {
                log.debug("Update 不包含消息，跳过处理");
                return;
            }

            Message message = update.getMessage();
            if (message == null) {
                log.warn("消息对象为空");
                return;
            }

            // 处理文本消息
            if (message.hasText()) {
                handleTextMessage(update, message);
            } else {
                log.debug("收到非文本消息，类型未处理");
            }

        } catch (Exception e) {
            log.error("处理 Telegram 更新时发生错误", e);
        }
    }

    /**
     * 处理文本消息
     */
    private void handleTextMessage(Update update, Message message) {
        try {
            Chat chat = message.getChat();
            String text = message.getText();
            String username = message.getFrom() != null ? message.getFrom().getUserName() : "Unknown";
            String chatTitle = chat != null ? chat.getTitle() : "Private";

            log.info("【TELEGRAM】[{}]-[{}]发送消息：{}", chatTitle, username, text);

            // 基础命令处理
            if (text.startsWith("/")) {
                handleCommand(message, text);
            }

        } catch (Exception e) {
            log.error("处理文本消息时发生错误", e);
        }
    }

    /**
     * 处理命令 - 使用策略模式和命令工厂
     */
    private void handleCommand(Message message, String text) {
        try {
            // 解析命令和参数
            String[] parts = text.trim().split("\\s+");
            String commandText = parts[0];
            String[] args = parts.length > 1 ?
                java.util.Arrays.copyOfRange(parts, 1, parts.length) : new String[0];

            // 获取对应的命令处理器
            CommandHandler handler = commandHandlerFactory.getHandler(commandText);

            // 执行命令处理
            boolean success = handler.handle(message, text, args);

            if (!success) {
                log.warn("命令处理失败: {}", commandText);
            }

        } catch (Exception e) {
            log.error("处理命令时发生未知错误: {}", text, e);
            Long chatId = message.getChatId();
            if (chatId != null) {
                messageService.sendErrorMessage(chatId, "命令处理失败，请稍后重试");
            }
        }
    }



    @Override
    public void onUpdatesReceived(List<Update> updates) {
        super.onUpdatesReceived(updates);
    }

    @Override
    public String getBotUsername() {
        return botConfig.getUsername();
    }

    @Override
    public String getBotToken() {
        return botConfig.getToken();
    }
}