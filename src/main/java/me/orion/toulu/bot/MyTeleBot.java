package me.orion.toulu.bot;

import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.telegram.telegrambots.bots.TelegramLongPollingBot;
import org.telegram.telegrambots.meta.TelegramBotsApi;
import org.telegram.telegrambots.meta.api.objects.Chat;
import org.telegram.telegrambots.meta.api.objects.Message;
import org.telegram.telegrambots.meta.api.objects.Update;
import org.telegram.telegrambots.meta.exceptions.TelegramApiException;
import org.telegram.telegrambots.updatesreceivers.DefaultBotSession;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class MyTeleBot extends TelegramLongPollingBot {

    private final BotConfig botConfig;

    @PostConstruct
    public void init() {
        TelegramBotsApi telegramBotsApi = null;
        try {
            telegramBotsApi = new TelegramBotsApi(DefaultBotSession.class);
            telegramBotsApi.registerBot(this);
            log.info("TG机器人启动成功");
        } catch (TelegramApiException e) {
            log.error("TG机器人启动失败：{}", e.getMessage());
        }
    }

    @Override
    public void onUpdateReceived(Update update) {
        Chat chat = update.getMessage().getChat();
        Message message = update.getMessage();
        if (update.hasMessage() && update.getMessage().hasText()) {
            log.info("【TELEGRAM】[{}]-[{}]发送消息：{}", chat.getTitle(), message.getFrom().getUserName(), message.getText());
        }
    }

    @Override
    public void onUpdatesReceived(List<Update> updates) {
        super.onUpdatesReceived(updates);
    }

    @Override
    public String getBotUsername() {
        return botConfig.getUsername();
    }

    @Override
    public String getBotToken() {
        return botConfig.getToken();
    }
}