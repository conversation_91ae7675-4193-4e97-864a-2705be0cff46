package me.orion.toulu.bot.command.handler;

import me.orion.toulu.bot.BotConfig;
import me.orion.toulu.bot.command.AbstractCommandHandler;
import me.orion.toulu.bot.command.BotCommand;
import me.orion.toulu.bot.service.TelegramMessageService;
import me.orion.toulu.bot.session.SessionManager;
import me.orion.toulu.bot.template.MessageTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.telegram.telegrambots.meta.api.objects.Message;

import java.util.HashMap;
import java.util.Map;

/**
 * /start 命令处理器
 * 处理用户开始使用机器人的欢迎流程
 * 
 * <AUTHOR> 4.0 sonnet
 */
@Component
public class StartCommandHandler extends AbstractCommandHandler {

    @Autowired
    private SessionManager sessionManager;

    @Autowired
    private MessageTemplate messageTemplate;

    public StartCommandHandler(TelegramMessageService messageService, BotConfig botConfig) {
        super(messageService, botConfig);
    }
    
    @Override
    public BotCommand getSupportedCommand() {
        return BotCommand.START;
    }
    
    @Override
    protected boolean executeCommand(Message message, String commandText, String[] args) {
        Long chatId = getChatId(message);
        Long userId = getUserId(message);
        String username = getUsername(message);

        // 创建或获取用户会话
        sessionManager.getOrCreateSession(userId, chatId, username);

        // 使用消息模板生成欢迎消息
        Map<String, Object> params = new HashMap<>();
        params.put("username", username);

        String welcomeMessage = messageTemplate.getMessage("welcome", "zh", params);

        return messageService.sendMessage(chatId, welcomeMessage);
    }
    
    @Override
    public String getDetailedHelp() {
        return getSupportedCommand().getCommand() + " - " + getSupportedCommand().getDescription() + 
               "\n\n这个命令会显示欢迎信息，是开始使用机器人的第一步。";
    }
}
