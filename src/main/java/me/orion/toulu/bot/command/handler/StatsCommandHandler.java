package me.orion.toulu.bot.command.handler;

import me.orion.toulu.bot.BotConfig;
import me.orion.toulu.bot.command.AbstractCommandHandler;
import me.orion.toulu.bot.command.BotCommand;
import me.orion.toulu.bot.service.TelegramMessageService;
import me.orion.toulu.bot.stats.BotStatisticsService;
import me.orion.toulu.bot.template.MessageTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.telegram.telegrambots.meta.api.objects.Message;

import java.util.HashMap;
import java.util.Map;

/**
 * /stats 命令处理器
 * 显示使用统计信息
 * 
 * <AUTHOR> 4.0 sonnet
 */
@Component
public class StatsCommandHandler extends AbstractCommandHandler {
    
    @Autowired
    private BotStatisticsService statisticsService;
    
    @Autowired
    private MessageTemplate messageTemplate;
    
    public StatsCommandHandler(TelegramMessageService messageService, BotConfig botConfig) {
        super(messageService, botConfig);
    }
    
    @Override
    public BotCommand getSupportedCommand() {
        return BotCommand.STATS;
    }
    
    @Override
    protected boolean executeCommand(Message message, String commandText, String[] args) {
        Long userId = getUserId(message);
        String language = "zh"; // 可以从用户会话中获取语言偏好
        
        // 获取用户统计信息
        BotStatisticsService.UserStatistics userStats = statisticsService.getUserStatistics(userId);
        if (userStats == null) {
            return sendError(message, "无法获取统计信息");
        }
        
        // 构建统计消息
        StringBuilder statsMessage = new StringBuilder();
        
        // 添加标题
        statsMessage.append(messageTemplate.getMessage("stats.header", language));
        
        // 用户统计
        Map<String, Object> userParams = new HashMap<>();
        userParams.put("commandCount", userStats.getCommandCount());
        userParams.put("messageCount", userStats.getMessageCount());
        userParams.put("duration", userStats.getSessionDurationMinutes());
        
        statsMessage.append(messageTemplate.getMessage("stats.user.format", language, userParams));
        
        // 如果是管理员，显示系统统计
        if (botConfig.isAdmin(userId)) {
            BotStatisticsService.GlobalStatistics globalStats = statisticsService.getGlobalStatistics();
            
            Map<String, Object> systemParams = new HashMap<>();
            systemParams.put("activeSessions", globalStats.getActiveSessions());
            systemParams.put("totalSessions", globalStats.getTotalSessionsCreated());
            systemParams.put("adminSessions", globalStats.getAdminSessions());
            
            statsMessage.append(messageTemplate.getMessage("stats.system.format", language, systemParams));
            
            // 添加详细的命令统计
            statsMessage.append("📊 <b>命令使用统计：</b>\n");
            Map<BotCommand, Long> commandStats = statisticsService.getCommandStatistics();
            
            commandStats.entrySet().stream()
                    .sorted(Map.Entry.<BotCommand, Long>comparingByValue().reversed())
                    .limit(5) // 显示前5个最常用的命令
                    .forEach(entry -> {
                        BotCommand command = entry.getKey();
                        Long count = entry.getValue();
                        statsMessage.append(String.format("• %s: %d 次\n", 
                            command.getDisplayName(), count));
                    });
            
            statsMessage.append(String.format("\n🕐 <b>运行时间：</b> %d 分钟\n", globalStats.getUptimeMinutes()));
            statsMessage.append(String.format("📨 <b>总消息数：</b> %d\n", globalStats.getTotalMessages()));
            statsMessage.append(String.format("❌ <b>总错误数：</b> %d\n", globalStats.getTotalErrors()));
        }
        
        // 添加个人最喜欢的命令
        if (userStats.getFavoriteCommand() != null) {
            statsMessage.append(String.format("\n⭐ <b>您最常用的命令：</b> %s", 
                userStats.getFavoriteCommand().getDisplayName()));
        }
        
        return sendInfo(message, statsMessage.toString());
    }
    
    @Override
    public String getDetailedHelp() {
        return getSupportedCommand().getCommand() + " - " + getSupportedCommand().getDescription() + 
               "\n\n显示您的使用统计信息，包括命令执行次数、消息数量和会话时长。\n" +
               "管理员用户还可以查看系统整体统计信息。";
    }
}
