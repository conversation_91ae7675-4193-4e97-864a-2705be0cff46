package me.orion.toulu.bot.command;

import lombok.Getter;

/**
 * Bot 命令枚举
 * 定义所有可用的 Telegram Bot 命令
 * 
 * <AUTHOR> 4.0 sonnet
 */
@Getter
public enum BotCommand {
    
    /**
     * 开始命令 - 欢迎新用户
     */
    START("/start", "开始使用机器人", "🚀 开始", false, true),
    
    /**
     * 帮助命令 - 显示帮助信息
     */
    HELP("/help", "显示帮助信息", "❓ 帮助", false, true),
    
    /**
     * 状态命令 - 显示机器人状态（管理员专用）
     */
    STATUS("/status", "查看机器人状态", "📊 状态", true, true),
    
    /**
     * 设置命令 - 用户设置（管理员专用）
     */
    SETTINGS("/settings", "机器人设置", "⚙️ 设置", true, true),

    /**
     * 安全命令 - 安全管理（管理员专用）
     */
    SECURITY("/security", "安全管理", "🔒 安全", true, true),
    
    /**
     * 统计命令 - 使用统计
     */
    STATS("/stats", "查看使用统计", "📈 统计", false, true),
    
    /**
     * 关于命令 - 关于信息
     */
    ABOUT("/about", "关于机器人", "ℹ️ 关于", false, true),
    
    /**
     * 未知命令 - 处理无法识别的命令
     */
    UNKNOWN("", "未知命令", "❓ 未知", false, false);
    
    /**
     * 命令字符串（如 "/start"）
     */
    private final String command;
    
    /**
     * 命令描述
     */
    private final String description;
    
    /**
     * 命令显示名称（用于按钮等）
     */
    private final String displayName;
    
    /**
     * 是否需要管理员权限
     */
    private final boolean adminOnly;
    
    /**
     * 是否在帮助中显示
     */
    private final boolean showInHelp;
    
    BotCommand(String command, String description, String displayName, boolean adminOnly, boolean showInHelp) {
        this.command = command;
        this.description = description;
        this.displayName = displayName;
        this.adminOnly = adminOnly;
        this.showInHelp = showInHelp;
    }
    
    /**
     * 根据命令字符串获取对应的枚举
     */
    public static BotCommand fromString(String commandText) {
        if (commandText == null || commandText.trim().isEmpty()) {
            return UNKNOWN;
        }
        
        // 提取命令部分（去除参数）
        String command = commandText.split(" ")[0].toLowerCase().trim();
        
        for (BotCommand botCommand : values()) {
            if (botCommand.command.equals(command)) {
                return botCommand;
            }
        }
        
        return UNKNOWN;
    }
    
    /**
     * 获取所有可在帮助中显示的命令
     */
    public static BotCommand[] getHelpCommands() {
        return java.util.Arrays.stream(values())
                .filter(cmd -> cmd.showInHelp && cmd != UNKNOWN)
                .toArray(BotCommand[]::new);
    }
    
    /**
     * 获取所有用户可用的命令（非管理员）
     */
    public static BotCommand[] getUserCommands() {
        return java.util.Arrays.stream(values())
                .filter(cmd -> !cmd.adminOnly && cmd.showInHelp && cmd != UNKNOWN)
                .toArray(BotCommand[]::new);
    }
    
    /**
     * 获取所有管理员命令
     */
    public static BotCommand[] getAdminCommands() {
        return java.util.Arrays.stream(values())
                .filter(cmd -> cmd.adminOnly && cmd.showInHelp && cmd != UNKNOWN)
                .toArray(BotCommand[]::new);
    }
    
    /**
     * 检查命令是否有效（非 UNKNOWN）
     */
    public boolean isValid() {
        return this != UNKNOWN;
    }
}
