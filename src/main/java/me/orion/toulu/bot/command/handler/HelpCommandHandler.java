package me.orion.toulu.bot.command.handler;

import me.orion.toulu.bot.BotConfig;
import me.orion.toulu.bot.command.AbstractCommandHandler;
import me.orion.toulu.bot.command.BotCommand;
import me.orion.toulu.bot.service.TelegramMessageService;
import org.springframework.stereotype.Component;
import org.telegram.telegrambots.meta.api.objects.Message;

/**
 * /help 命令处理器
 * 显示帮助信息和可用命令列表
 * 
 * <AUTHOR> 4.0 sonnet
 */
@Component
public class HelpCommandHandler extends AbstractCommandHandler {
    
    public HelpCommandHandler(TelegramMessageService messageService, BotConfig botConfig) {
        super(messageService, botConfig);
    }
    
    @Override
    public BotCommand getSupportedCommand() {
        return BotCommand.HELP;
    }
    
    @Override
    protected boolean executeCommand(Message message, String commandText, String[] args) {
        Long chatId = getChatId(message);
        Long userId = getUserId(message);
        
        // 构建帮助消息
        StringBuilder helpMessage = new StringBuilder();
        helpMessage.append("📋 <b>可用命令</b>\n\n");
        
        // 添加用户命令
        helpMessage.append("👤 <b>用户命令：</b>\n");
        for (BotCommand command : BotCommand.getUserCommands()) {
            helpMessage.append(String.format("🔹 %s - %s\n", 
                command.getCommand(), command.getDescription()));
        }
        
        // 如果是管理员，显示管理员命令
        if (botConfig.isAdmin(userId)) {
            helpMessage.append("\n👑 <b>管理员命令：</b>\n");
            for (BotCommand command : BotCommand.getAdminCommands()) {
                helpMessage.append(String.format("🔸 %s - %s\n", 
                    command.getCommand(), command.getDescription()));
            }
        }
        
        helpMessage.append("\n💡 <i>提示：点击命令可以直接使用</i>");
        
        return messageService.sendMessage(chatId, helpMessage.toString());
    }
    
    @Override
    public String getDetailedHelp() {
        return getSupportedCommand().getCommand() + " - " + getSupportedCommand().getDescription() + 
               "\n\n显示所有可用命令的列表和说明。管理员用户还会看到额外的管理员命令。";
    }
}
