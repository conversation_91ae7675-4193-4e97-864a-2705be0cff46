package me.orion.toulu.bot.command;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 命令处理器工厂
 * 负责管理和分发命令处理器
 * 
 * <AUTHOR> 4.0 sonnet
 */
@Component
@Slf4j
public class CommandHandlerFactory {
    
    @Autowired
    private List<CommandHandler> commandHandlers;
    
    private final Map<BotCommand, CommandHandler> handlerMap = new HashMap<>();
    private CommandHandler unknownCommandHandler;
    
    /**
     * 初始化处理器映射
     */
    @PostConstruct
    public void initializeHandlers() {
        log.info("初始化命令处理器...");
        
        for (CommandHandler handler : commandHandlers) {
            BotCommand command = handler.getSupportedCommand();
            handlerMap.put(command, handler);
            
            if (command == BotCommand.UNKNOWN) {
                unknownCommandHandler = handler;
            }
            
            log.debug("注册命令处理器: {} -> {}", 
                command.getCommand(), handler.getClass().getSimpleName());
        }
        
        log.info("命令处理器初始化完成，共注册 {} 个处理器", handlerMap.size());
        
        // 验证必要的处理器是否存在
        validateHandlers();
    }
    
    /**
     * 根据命令文本获取对应的处理器
     */
    public CommandHandler getHandler(String commandText) {
        BotCommand command = BotCommand.fromString(commandText);
        return getHandler(command);
    }
    
    /**
     * 根据命令枚举获取对应的处理器
     */
    public CommandHandler getHandler(BotCommand command) {
        CommandHandler handler = handlerMap.get(command);
        
        if (handler == null) {
            log.warn("未找到命令 {} 的处理器，使用未知命令处理器", command.getCommand());
            return unknownCommandHandler;
        }
        
        return handler;
    }
    
    /**
     * 获取所有已注册的处理器
     */
    public Map<BotCommand, CommandHandler> getAllHandlers() {
        return new HashMap<>(handlerMap);
    }
    
    /**
     * 检查是否支持指定命令
     */
    public boolean isSupported(String commandText) {
        BotCommand command = BotCommand.fromString(commandText);
        return command.isValid() && handlerMap.containsKey(command);
    }
    
    /**
     * 检查是否支持指定命令枚举
     */
    public boolean isSupported(BotCommand command) {
        return command.isValid() && handlerMap.containsKey(command);
    }
    
    /**
     * 获取支持的命令列表
     */
    public String getSupportedCommands() {
        StringBuilder sb = new StringBuilder();
        sb.append("支持的命令：\n");
        
        for (BotCommand command : BotCommand.values()) {
            if (command.isValid() && handlerMap.containsKey(command)) {
                sb.append("• ").append(command.getCommand())
                  .append(" - ").append(command.getDescription()).append("\n");
            }
        }
        
        return sb.toString();
    }
    
    /**
     * 验证必要的处理器是否存在
     */
    private void validateHandlers() {
        // 检查未知命令处理器
        if (unknownCommandHandler == null) {
            log.error("未找到未知命令处理器，这可能导致未知命令无法正确处理");
        }
        
        // 检查基础命令处理器
        BotCommand[] essentialCommands = {BotCommand.START, BotCommand.HELP};
        for (BotCommand command : essentialCommands) {
            if (!handlerMap.containsKey(command)) {
                log.warn("未找到基础命令 {} 的处理器", command.getCommand());
            }
        }
    }
    
    /**
     * 获取处理器统计信息
     */
    public String getHandlerStats() {
        return String.format(
            "命令处理器统计：\n" +
            "• 总处理器数量: %d\n" +
            "• 用户命令处理器: %d\n" +
            "• 管理员命令处理器: %d\n" +
            "• 未知命令处理器: %s",
            handlerMap.size(),
            (int) handlerMap.keySet().stream().filter(cmd -> !cmd.isAdminOnly()).count(),
            (int) handlerMap.keySet().stream().filter(BotCommand::isAdminOnly).count(),
            unknownCommandHandler != null ? "已注册" : "未注册"
        );
    }
}
