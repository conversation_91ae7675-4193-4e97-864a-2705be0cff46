package me.orion.toulu.bot.command.handler;

import me.orion.toulu.bot.BotConfig;
import me.orion.toulu.bot.command.AbstractCommandHandler;
import me.orion.toulu.bot.command.BotCommand;
import me.orion.toulu.bot.security.BotSecurityService;
import me.orion.toulu.bot.service.TelegramMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.telegram.telegrambots.meta.api.objects.Message;

/**
 * /security 命令处理器
 * 安全管理功能（管理员专用）
 * 
 * <AUTHOR> 4.0 sonnet
 */
@Component
public class SecurityCommandHandler extends AbstractCommandHandler {
    
    @Autowired
    private BotSecurityService securityService;
    
    public SecurityCommandHandler(TelegramMessageService messageService, BotConfig botConfig) {
        super(messageService, botConfig);
    }
    
    @Override
    public BotCommand getSupportedCommand() {
        return BotCommand.SECURITY;
    }
    
    @Override
    protected boolean executeCommand(Message message, String commandText, String[] args) {
        Long chatId = getChatId(message);
        Long userId = getUserId(message);
        
        if (args.length == 0) {
            return showSecurityOverview(chatId);
        }
        
        String subCommand = args[0].toLowerCase();
        
        switch (subCommand) {
            case "overview":
                return showSecurityOverview(chatId);
                
            case "user":
                if (args.length < 2) {
                    return sendError(message, "请提供用户ID: /security user <userId>");
                }
                return showUserSecurity(chatId, args[1]);
                
            case "blacklist":
                if (args.length < 3) {
                    return sendError(message, "用法: /security blacklist <add|remove> <userId> [reason]");
                }
                return handleBlacklist(chatId, args);
                
            case "help":
                return showSecurityHelp(chatId);
                
            default:
                return sendError(message, "未知的安全命令。使用 /security help 查看帮助");
        }
    }
    
    /**
     * 显示安全概览
     */
    private boolean showSecurityOverview(Long chatId) {
        BotSecurityService.SecurityOverview overview = securityService.getSecurityOverview();
        
        String overviewMessage = String.format(
            "🔒 <b>安全概览</b>\n\n" +
            "📊 <b>统计信息：</b>\n" +
            "• 活跃速率限制记录: %d\n" +
            "• 黑名单用户数: %d\n" +
            "• 可疑用户数: %d\n\n" +
            "💡 使用 <code>/security help</code> 查看更多命令",
            overview.getActiveRateLimitRecords(),
            overview.getBlacklistedUserCount(),
            overview.getSuspiciousUserCount()
        );
        
        return sendInfo(getChatMessage(chatId), overviewMessage);
    }
    
    /**
     * 显示用户安全信息
     */
    private boolean showUserSecurity(Long chatId, String userIdStr) {
        try {
            Long targetUserId = Long.parseLong(userIdStr);
            BotSecurityService.UserSecurityStats stats = securityService.getUserSecurityStats(targetUserId);
            
            if (stats == null) {
                return sendError(getChatMessage(chatId), "未找到用户 " + targetUserId + " 的安全信息");
            }
            
            String userSecurityMessage = String.format(
                "👤 <b>用户安全信息</b>\n\n" +
                "🆔 <b>用户ID:</b> <code>%d</code>\n" +
                "🚫 <b>黑名单状态:</b> %s\n" +
                "⏱️ <b>最近1分钟请求:</b> %d\n" +
                "🕐 <b>最近1小时请求:</b> %d\n" +
                "⚠️ <b>可疑活动次数:</b> %d",
                stats.getUserId(),
                stats.isBlacklisted() ? "❌ 已拉黑" : "✅ 正常",
                stats.getRequestsInLastMinute(),
                stats.getRequestsInLastHour(),
                stats.getSuspiciousActivityCount()
            );
            
            return sendInfo(getChatMessage(chatId), userSecurityMessage);
            
        } catch (NumberFormatException e) {
            return sendError(getChatMessage(chatId), "无效的用户ID: " + userIdStr);
        }
    }
    
    /**
     * 处理黑名单操作
     */
    private boolean handleBlacklist(Long chatId, String[] args) {
        String action = args[1].toLowerCase();
        String userIdStr = args[2];
        
        try {
            Long targetUserId = Long.parseLong(userIdStr);
            
            switch (action) {
                case "add":
                    String reason = args.length > 3 ? String.join(" ", 
                            java.util.Arrays.copyOfRange(args, 3, args.length)) : "管理员操作";
                    securityService.addToBlacklist(targetUserId, reason);
                    return sendSuccess(getChatMessage(chatId), 
                            String.format("用户 %d 已添加到黑名单，原因: %s", targetUserId, reason));
                    
                case "remove":
                    securityService.removeFromBlacklist(targetUserId);
                    return sendSuccess(getChatMessage(chatId), 
                            String.format("用户 %d 已从黑名单移除", targetUserId));
                    
                default:
                    return sendError(getChatMessage(chatId), "无效的操作: " + action + "。使用 add 或 remove");
            }
            
        } catch (NumberFormatException e) {
            return sendError(getChatMessage(chatId), "无效的用户ID: " + userIdStr);
        }
    }
    
    /**
     * 显示安全帮助
     */
    private boolean showSecurityHelp(Long chatId) {
        String helpMessage = 
            "🔒 <b>安全管理命令</b>\n\n" +
            "📋 <b>可用命令：</b>\n" +
            "• <code>/security overview</code> - 安全概览\n" +
            "• <code>/security user &lt;userId&gt;</code> - 查看用户安全信息\n" +
            "• <code>/security blacklist add &lt;userId&gt; [reason]</code> - 添加到黑名单\n" +
            "• <code>/security blacklist remove &lt;userId&gt;</code> - 从黑名单移除\n" +
            "• <code>/security help</code> - 显示此帮助\n\n" +
            "⚠️ <b>注意：</b>所有安全命令仅限管理员使用";
        
        return sendInfo(getChatMessage(chatId), helpMessage);
    }
    
    /**
     * 创建消息对象的辅助方法
     */
    private Message getChatMessage(Long chatId) {
        // 这是一个简化的实现，实际使用中可能需要更完整的 Message 对象
        return new Message() {
            @Override
            public Long getChatId() {
                return chatId;
            }
        };
    }
    
    @Override
    public String getDetailedHelp() {
        return getSupportedCommand().getCommand() + " - " + getSupportedCommand().getDescription() + 
               "\n\n安全管理功能，包括用户黑名单管理、安全统计查看等。\n" +
               "⚠️ 此命令仅限管理员使用。\n\n" +
               "使用 /security help 查看详细的子命令说明。";
    }
}
