package me.orion.toulu.bot.command;

import org.telegram.telegrambots.meta.api.objects.Message;

/**
 * 命令处理器接口
 * 定义命令处理的统一规范
 * 
 * <AUTHOR> 4.0 sonnet
 */
public interface CommandHandler {
    
    /**
     * 获取该处理器支持的命令
     */
    BotCommand getSupportedCommand();
    
    /**
     * 处理命令
     * 
     * @param message Telegram 消息对象
     * @param commandText 完整的命令文本
     * @param args 命令参数数组
     * @return 处理结果，true 表示成功，false 表示失败
     */
    boolean handle(Message message, String commandText, String[] args);
    
    /**
     * 检查用户是否有权限执行此命令
     * 
     * @param userId 用户ID
     * @return true 表示有权限，false 表示无权限
     */
    default boolean hasPermission(Long userId) {
        return true; // 默认所有用户都有权限
    }
    
    /**
     * 获取命令的使用说明
     */
    default String getUsage() {
        return getSupportedCommand().getCommand() + " - " + getSupportedCommand().getDescription();
    }
    
    /**
     * 获取命令的详细帮助信息
     */
    default String getDetailedHelp() {
        return getUsage();
    }
}
