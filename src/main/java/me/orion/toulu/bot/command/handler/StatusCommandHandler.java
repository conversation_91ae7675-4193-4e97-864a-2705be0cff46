package me.orion.toulu.bot.command.handler;

import me.orion.toulu.bot.BotConfig;
import me.orion.toulu.bot.command.AbstractCommandHandler;
import me.orion.toulu.bot.command.BotCommand;
import me.orion.toulu.bot.service.TelegramMessageService;
import org.springframework.stereotype.Component;
import org.telegram.telegrambots.meta.api.objects.Message;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * /status 命令处理器
 * 显示机器人状态信息（管理员专用）
 * 
 * <AUTHOR> 4.0 sonnet
 */
@Component
public class StatusCommandHandler extends AbstractCommandHandler {
    
    private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    public StatusCommandHandler(TelegramMessageService messageService, BotConfig botConfig) {
        super(messageService, botConfig);
    }
    
    @Override
    public BotCommand getSupportedCommand() {
        return BotCommand.STATUS;
    }
    
    @Override
    protected boolean executeCommand(Message message, String commandText, String[] args) {
        Long chatId = getChatId(message);
        
        // 获取系统信息
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        long maxMemory = runtime.maxMemory();
        
        // 构建状态信息
        String statusInfo = String.format(
            "🤖 <b>机器人状态信息</b>\n\n" +
            "📊 <b>基本信息：</b>\n" +
            "• Bot 用户名: <code>%s</code>\n" +
            "• 当前时间: <code>%s</code>\n" +
            "• 运行状态: ✅ 正常运行\n\n" +
            
            "⚙️ <b>配置信息：</b>\n" +
            "• 调试模式: %s\n" +
            "• 连接超时: %d 秒\n" +
            "• 读取超时: %d 秒\n" +
            "• 最大重试次数: %d\n\n" +
            
            "💾 <b>内存使用：</b>\n" +
            "• 已用内存: %.2f MB\n" +
            "• 总内存: %.2f MB\n" +
            "• 最大内存: %.2f MB\n" +
            "• 内存使用率: %.1f%%\n\n" +
            
            "🔧 <b>JVM 信息：</b>\n" +
            "• Java 版本: <code>%s</code>\n" +
            "• 可用处理器: %d\n",
            
            botConfig.getUsername(),
            LocalDateTime.now().format(formatter),
            botConfig.isDebugMode() ? "🟢 开启" : "🔴 关闭",
            botConfig.getConnectionTimeout(),
            botConfig.getReadTimeout(),
            botConfig.getMaxRetryAttempts(),
            usedMemory / 1024.0 / 1024.0,
            totalMemory / 1024.0 / 1024.0,
            maxMemory / 1024.0 / 1024.0,
            (usedMemory * 100.0) / totalMemory,
            System.getProperty("java.version"),
            runtime.availableProcessors()
        );
        
        return sendInfo(message, statusInfo);
    }
    
    @Override
    public String getDetailedHelp() {
        return getSupportedCommand().getCommand() + " - " + getSupportedCommand().getDescription() + 
               "\n\n显示机器人的详细状态信息，包括配置、内存使用情况和系统信息。\n" +
               "⚠️ 此命令仅限管理员使用。";
    }
}
