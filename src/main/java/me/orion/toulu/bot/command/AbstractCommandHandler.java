package me.orion.toulu.bot.command;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.orion.toulu.bot.BotConfig;
import me.orion.toulu.bot.service.TelegramMessageService;
import me.orion.toulu.bot.security.BotSecurityService;
import me.orion.toulu.bot.session.SessionManager;
import me.orion.toulu.bot.stats.BotStatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Timer;
import org.telegram.telegrambots.meta.api.objects.Message;
import org.telegram.telegrambots.meta.api.objects.User;

/**
 * 抽象命令处理器基类
 * 提供通用的命令处理逻辑和工具方法
 * 
 * <AUTHOR> 4.0 sonnet
 */
@Slf4j
@RequiredArgsConstructor
public abstract class AbstractCommandHandler implements CommandHandler {
    
    protected final TelegramMessageService messageService;
    protected final BotConfig botConfig;

    @Autowired
    protected SessionManager sessionManager;

    @Autowired
    protected BotStatisticsService statisticsService;

    @Autowired
    protected BotSecurityService securityService;

    @Autowired
    protected Counter commandExecutionCounter;

    @Autowired
    protected Timer commandExecutionTimer;
    
    @Override
    public final boolean handle(Message message, String commandText, String[] args) {
        Timer.Sample sample = Timer.start();
        try {
            Long chatId = message.getChatId();
            Long userId = getUserId(message);
            String username = getUsername(message);

            // 安全检查：速率限制
            if (securityService.isRateLimited(userId)) {
                log.warn("用户 {} 触发速率限制", userId);
                messageService.sendWarningMessage(chatId, "⚠️ 请求过于频繁，请稍后再试");
                return false;
            }

            // 记录命令使用
            log.info("用户 {} ({}) 执行命令: {}", username, userId, getSupportedCommand().getCommand());

            // 权限检查
            if (!hasPermission(userId)) {
                log.warn("用户 {} 尝试执行无权限命令: {}", userId, getSupportedCommand().getCommand());
                messageService.sendWarningMessage(chatId, "❌ 您没有权限执行此命令");
                return false;
            }

            // 确保用户会话存在
            sessionManager.getOrCreateSession(userId, chatId, username);

            // 记录统计信息
            statisticsService.recordCommandUsage(userId, getSupportedCommand());
            commandExecutionCounter.increment();

            // 执行具体的命令处理逻辑
            boolean success = executeCommand(message, commandText, args);

            if (!success) {
                statisticsService.recordError(userId);
            }

            return success;
            
        } catch (Exception e) {
            log.error("处理命令 {} 时发生错误", getSupportedCommand().getCommand(), e);
            handleError(message, e);
            return false;
        } finally {
            sample.stop(commandExecutionTimer);
        }
    }
    
    /**
     * 子类实现具体的命令处理逻辑
     */
    protected abstract boolean executeCommand(Message message, String commandText, String[] args);
    
    /**
     * 权限检查（可被子类重写）
     */
    @Override
    public boolean hasPermission(Long userId) {
        BotCommand command = getSupportedCommand();
        
        // 如果命令需要管理员权限，检查用户是否为管理员
        if (command.isAdminOnly()) {
            return botConfig.isAdmin(userId);
        }
        
        return true; // 普通命令，所有用户都有权限
    }
    
    /**
     * 错误处理
     */
    protected void handleError(Message message, Exception e) {
        Long chatId = message.getChatId();
        String errorMessage = String.format("处理命令 %s 时发生错误，请稍后重试", getSupportedCommand().getDisplayName());
        messageService.sendErrorMessage(chatId, errorMessage);
    }
    
    /**
     * 获取用户ID
     */
    protected Long getUserId(Message message) {
        User from = message.getFrom();
        return from != null ? from.getId() : null;
    }
    
    /**
     * 获取用户名
     */
    protected String getUsername(Message message) {
        User from = message.getFrom();
        if (from == null) {
            return "Unknown";
        }
        
        String username = from.getUserName();
        if (username != null && !username.trim().isEmpty()) {
            return username;
        }
        
        String firstName = from.getFirstName();
        String lastName = from.getLastName();
        
        if (firstName != null && !firstName.trim().isEmpty()) {
            if (lastName != null && !lastName.trim().isEmpty()) {
                return firstName + " " + lastName;
            }
            return firstName;
        }
        
        return "User" + from.getId();
    }
    
    /**
     * 获取聊天ID
     */
    protected Long getChatId(Message message) {
        return message.getChatId();
    }
    
    /**
     * 发送成功消息的便捷方法
     */
    protected boolean sendSuccess(Message message, String text) {
        return messageService.sendSuccessMessage(getChatId(message), text);
    }
    
    /**
     * 发送错误消息的便捷方法
     */
    protected boolean sendError(Message message, String text) {
        return messageService.sendErrorMessage(getChatId(message), text);
    }
    
    /**
     * 发送警告消息的便捷方法
     */
    protected boolean sendWarning(Message message, String text) {
        return messageService.sendWarningMessage(getChatId(message), text);
    }
    
    /**
     * 发送信息消息的便捷方法
     */
    protected boolean sendInfo(Message message, String text) {
        return messageService.sendInfoMessage(getChatId(message), text);
    }
    
    /**
     * 发送普通消息的便捷方法
     */
    protected boolean sendMessage(Message message, String text) {
        return messageService.sendMessage(getChatId(message), text);
    }
}
