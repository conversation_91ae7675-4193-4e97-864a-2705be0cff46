package me.orion.toulu.bot.command.handler;

import me.orion.toulu.bot.BotConfig;
import me.orion.toulu.bot.command.AbstractCommandHandler;
import me.orion.toulu.bot.command.BotCommand;
import me.orion.toulu.bot.service.TelegramMessageService;
import org.springframework.stereotype.Component;
import org.telegram.telegrambots.meta.api.objects.Message;

/**
 * /about 命令处理器
 * 显示机器人的相关信息
 * 
 * <AUTHOR> 4.0 sonnet
 */
@Component
public class AboutCommandHandler extends AbstractCommandHandler {
    
    public AboutCommandHandler(TelegramMessageService messageService, BotConfig botConfig) {
        super(messageService, botConfig);
    }
    
    @Override
    public BotCommand getSupportedCommand() {
        return BotCommand.ABOUT;
    }
    
    @Override
    protected boolean executeCommand(Message message, String commandText, String[] args) {
        Long chatId = getChatId(message);
        
        String aboutMessage = 
            "ℹ️ <b>关于 Toulu Bot</b>\n\n" +
            "🤖 <b>机器人信息：</b>\n" +
            "• 名称: Toulu Bot\n" +
            "• 版本: 1.0.0\n" +
            "• 开发者: Claude 4.0 sonnet\n" +
            "• 框架: Spring Boot + Telegram Bot API\n\n" +
            
            "🚀 <b>主要功能：</b>\n" +
            "• 智能命令处理\n" +
            "• 多用户权限管理\n" +
            "• 可扩展的插件架构\n" +
            "• 完善的错误处理\n\n" +
            
            "💡 <b>技术特性：</b>\n" +
            "• 基于策略模式的命令处理\n" +
            "• 支持异步消息处理\n" +
            "• 内置重试机制\n" +
            "• 完整的日志记录\n\n" +
            
            "📞 <b>联系方式：</b>\n" +
            "如有问题或建议，请联系管理员。";
        
        return sendInfo(message, aboutMessage);
    }
    
    @Override
    public String getDetailedHelp() {
        return getSupportedCommand().getCommand() + " - " + getSupportedCommand().getDescription() + 
               "\n\n显示机器人的详细信息，包括版本、功能特性和技术架构等。";
    }
}
