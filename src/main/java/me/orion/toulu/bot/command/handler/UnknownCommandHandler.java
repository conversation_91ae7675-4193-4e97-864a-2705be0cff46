package me.orion.toulu.bot.command.handler;

import me.orion.toulu.bot.BotConfig;
import me.orion.toulu.bot.command.AbstractCommandHandler;
import me.orion.toulu.bot.command.BotCommand;
import me.orion.toulu.bot.service.TelegramMessageService;
import org.springframework.stereotype.Component;
import org.telegram.telegrambots.meta.api.objects.Message;

/**
 * 未知命令处理器
 * 处理无法识别的命令
 * 
 * <AUTHOR> 4.0 sonnet
 */
@Component
public class UnknownCommandHandler extends AbstractCommandHandler {
    
    public UnknownCommandHandler(TelegramMessageService messageService, BotConfig botConfig) {
        super(messageService, botConfig);
    }
    
    @Override
    public BotCommand getSupportedCommand() {
        return BotCommand.UNKNOWN;
    }
    
    @Override
    protected boolean executeCommand(Message message, String commandText, String[] args) {
        Long chatId = getChatId(message);
        
        String unknownMessage = String.format(
            "❓ <b>未知命令</b>\n\n" +
            "抱歉，我不认识命令 <code>%s</code>\n\n" +
            "💡 发送 /help 查看所有可用命令",
            commandText.split(" ")[0]
        );
        
        return messageService.sendMessage(chatId, unknownMessage);
    }
    
    @Override
    public String getDetailedHelp() {
        return "处理无法识别的命令，并提示用户查看帮助信息。";
    }
}
