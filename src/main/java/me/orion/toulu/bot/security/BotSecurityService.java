package me.orion.toulu.bot.security;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Bot 安全服务
 * 提供速率限制、黑名单管理等安全功能
 * 
 * <AUTHOR> 4.0 sonnet
 */
@Service
@Slf4j
public class BotSecurityService {
    
    /**
     * 用户速率限制记录
     */
    private final Map<Long, RateLimitRecord> rateLimitRecords = new ConcurrentHashMap<>();
    
    /**
     * 黑名单用户
     */
    private final Set<Long> blacklistedUsers = ConcurrentHashMap.newKeySet();
    
    /**
     * 可疑活动记录
     */
    private final Map<Long, SuspiciousActivityRecord> suspiciousActivities = new ConcurrentHashMap<>();
    
    /**
     * 默认速率限制配置
     */
    private static final int DEFAULT_MAX_REQUESTS_PER_MINUTE = 20;
    private static final int DEFAULT_MAX_REQUESTS_PER_HOUR = 100;
    private static final int SUSPICIOUS_THRESHOLD = 50; // 每分钟超过50次请求视为可疑
    
    /**
     * 检查用户是否被限制
     */
    public boolean isRateLimited(Long userId) {
        if (userId == null) {
            return true;
        }
        
        // 检查黑名单
        if (blacklistedUsers.contains(userId)) {
            log.warn("用户 {} 在黑名单中，拒绝请求", userId);
            return true;
        }
        
        RateLimitRecord record = rateLimitRecords.computeIfAbsent(userId, k -> new RateLimitRecord());
        LocalDateTime now = LocalDateTime.now();
        
        // 清理过期记录
        record.cleanupExpiredRecords(now);
        
        // 检查每分钟限制
        if (record.getRequestsInLastMinute(now) >= DEFAULT_MAX_REQUESTS_PER_MINUTE) {
            log.warn("用户 {} 超过每分钟请求限制: {}", userId, DEFAULT_MAX_REQUESTS_PER_MINUTE);
            recordSuspiciousActivity(userId, "RATE_LIMIT_EXCEEDED_MINUTE");
            return true;
        }
        
        // 检查每小时限制
        if (record.getRequestsInLastHour(now) >= DEFAULT_MAX_REQUESTS_PER_HOUR) {
            log.warn("用户 {} 超过每小时请求限制: {}", userId, DEFAULT_MAX_REQUESTS_PER_HOUR);
            recordSuspiciousActivity(userId, "RATE_LIMIT_EXCEEDED_HOUR");
            return true;
        }
        
        // 记录请求
        record.addRequest(now);
        
        // 检查是否为可疑活动
        if (record.getRequestsInLastMinute(now) > SUSPICIOUS_THRESHOLD) {
            recordSuspiciousActivity(userId, "HIGH_FREQUENCY_REQUESTS");
        }
        
        return false;
    }
    
    /**
     * 检查用户权限（带缓存）
     */
    @Cacheable(value = "userPermissions", key = "#userId")
    public boolean hasPermission(Long userId, String permission) {
        if (userId == null || permission == null) {
            return false;
        }
        
        // 检查黑名单
        if (blacklistedUsers.contains(userId)) {
            return false;
        }
        
        // 这里可以实现更复杂的权限检查逻辑
        // 例如从数据库查询用户角色和权限
        
        return true; // 默认允许
    }
    
    /**
     * 添加用户到黑名单
     */
    public void addToBlacklist(Long userId, String reason) {
        if (userId != null) {
            blacklistedUsers.add(userId);
            recordSuspiciousActivity(userId, "BLACKLISTED: " + reason);
            log.warn("用户 {} 已添加到黑名单，原因: {}", userId, reason);
        }
    }
    
    /**
     * 从黑名单移除用户
     */
    public void removeFromBlacklist(Long userId) {
        if (userId != null && blacklistedUsers.remove(userId)) {
            log.info("用户 {} 已从黑名单移除", userId);
        }
    }
    
    /**
     * 检查用户是否在黑名单中
     */
    public boolean isBlacklisted(Long userId) {
        return userId != null && blacklistedUsers.contains(userId);
    }
    
    /**
     * 记录可疑活动
     */
    private void recordSuspiciousActivity(Long userId, String activity) {
        SuspiciousActivityRecord record = suspiciousActivities.computeIfAbsent(
                userId, k -> new SuspiciousActivityRecord());
        record.addActivity(activity, LocalDateTime.now());
        
        log.warn("记录可疑活动 - 用户: {}, 活动: {}", userId, activity);
        
        // 如果可疑活动过多，自动加入黑名单
        if (record.getActivityCount() > 10) {
            addToBlacklist(userId, "EXCESSIVE_SUSPICIOUS_ACTIVITIES");
        }
    }
    
    /**
     * 获取用户安全统计
     */
    public UserSecurityStats getUserSecurityStats(Long userId) {
        if (userId == null) {
            return null;
        }
        
        RateLimitRecord rateLimitRecord = rateLimitRecords.get(userId);
        SuspiciousActivityRecord suspiciousRecord = suspiciousActivities.get(userId);
        
        return UserSecurityStats.builder()
                .userId(userId)
                .isBlacklisted(isBlacklisted(userId))
                .requestsInLastMinute(rateLimitRecord != null ? 
                        rateLimitRecord.getRequestsInLastMinute(LocalDateTime.now()) : 0)
                .requestsInLastHour(rateLimitRecord != null ? 
                        rateLimitRecord.getRequestsInLastHour(LocalDateTime.now()) : 0)
                .suspiciousActivityCount(suspiciousRecord != null ? 
                        suspiciousRecord.getActivityCount() : 0)
                .build();
    }
    
    /**
     * 获取安全统计概览
     */
    public SecurityOverview getSecurityOverview() {
        int activeRateLimitRecords = rateLimitRecords.size();
        int blacklistedUserCount = blacklistedUsers.size();
        int suspiciousUserCount = suspiciousActivities.size();
        
        return SecurityOverview.builder()
                .activeRateLimitRecords(activeRateLimitRecords)
                .blacklistedUserCount(blacklistedUserCount)
                .suspiciousUserCount(suspiciousUserCount)
                .build();
    }
    
    /**
     * 速率限制记录
     */
    @Data
    private static class RateLimitRecord {
        private final Map<LocalDateTime, AtomicInteger> requestCounts = new ConcurrentHashMap<>();
        
        public void addRequest(LocalDateTime time) {
            // 精确到分钟
            LocalDateTime minuteKey = time.withSecond(0).withNano(0);
            requestCounts.computeIfAbsent(minuteKey, k -> new AtomicInteger(0)).incrementAndGet();
        }
        
        public int getRequestsInLastMinute(LocalDateTime now) {
            LocalDateTime oneMinuteAgo = now.minusMinutes(1);
            return requestCounts.entrySet().stream()
                    .filter(entry -> entry.getKey().isAfter(oneMinuteAgo))
                    .mapToInt(entry -> entry.getValue().get())
                    .sum();
        }
        
        public int getRequestsInLastHour(LocalDateTime now) {
            LocalDateTime oneHourAgo = now.minusHours(1);
            return requestCounts.entrySet().stream()
                    .filter(entry -> entry.getKey().isAfter(oneHourAgo))
                    .mapToInt(entry -> entry.getValue().get())
                    .sum();
        }
        
        public void cleanupExpiredRecords(LocalDateTime now) {
            LocalDateTime cutoff = now.minusHours(2); // 保留2小时内的记录
            requestCounts.entrySet().removeIf(entry -> entry.getKey().isBefore(cutoff));
        }
    }
    
    /**
     * 可疑活动记录
     */
    @Data
    private static class SuspiciousActivityRecord {
        private final Map<String, LocalDateTime> activities = new ConcurrentHashMap<>();
        
        public void addActivity(String activity, LocalDateTime time) {
            activities.put(activity + "_" + time.toString(), time);
        }
        
        public int getActivityCount() {
            return activities.size();
        }
    }
    
    /**
     * 用户安全统计
     */
    @Data
    @lombok.Builder
    public static class UserSecurityStats {
        private Long userId;
        private boolean isBlacklisted;
        private int requestsInLastMinute;
        private int requestsInLastHour;
        private int suspiciousActivityCount;
    }
    
    /**
     * 安全概览
     */
    @Data
    @lombok.Builder
    public static class SecurityOverview {
        private int activeRateLimitRecords;
        private int blacklistedUserCount;
        private int suspiciousUserCount;
    }
}
