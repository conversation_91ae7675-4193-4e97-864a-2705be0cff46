spring:
  application:
    name: toulu
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ******************************************************************************************************************************************************************************************************************************************************************
    username: root
    password: mysql_xDhMmz

server:
  port: 18888

# Redis 配置（可选）
spring:
  data:
    redis:
      host: localhost
      port: 6379
      password:
      database: 0
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
          max-wait: -1ms

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true


telegram:
  token: **********:AAGsIlbKmkYFQIe9gqYJe9Qq928UeVF07dk
  username: locahl_test1123123_bot
  enabled: true
  connection-timeout: 30
  read-timeout: 60
  max-retry-attempts: 3
  retry-delay: 1000
  debug-mode: false
  admin-user-ids: ""  # 管理员用户ID，用逗号分隔
  welcome-message: "🤖 欢迎使用 Toulu Bot！\n\n这是一个功能强大的 Telegram 机器人。\n\n发送 /help 查看可用命令。"
  help-message: |
    📋 <b>可用命令</b>

    🔹 /start - 开始使用机器人
    🔹 /help - 显示此帮助信息
    🔹 /stats - 查看使用统计
    🔹 /about - 关于机器人
    🔹 /status - 查看机器人状态（管理员专用）
    🔹 /security - 安全管理（管理员专用）

    💡 <i>更多功能正在开发中...</i>